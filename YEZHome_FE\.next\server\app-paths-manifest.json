{"/[locale]/(protected)/user/bds/[propertyId]/page": "app/[locale]/(protected)/user/bds/[propertyId]/page.js", "/[locale]/(protected)/user/bds/new/page": "app/[locale]/(protected)/user/bds/new/page.js", "/[locale]/(protected)/user/bds/page": "app/[locale]/(protected)/user/bds/page.js", "/[locale]/(protected)/user/profile/page": "app/[locale]/(protected)/user/profile/page.js", "/_not-found/page": "app/_not-found/page.js", "/api/map/address-suggestions/route": "app/api/map/address-suggestions/route.js", "/api/map/cities/route": "app/api/map/cities/route.js", "/api/map/districts/route": "app/api/map/districts/route.js", "/api/map/place-detail/route": "app/api/map/place-detail/route.js", "/api/map/wards/route": "app/api/map/wards/route.js"}