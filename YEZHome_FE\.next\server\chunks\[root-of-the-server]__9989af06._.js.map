{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/api/map/place-detail/route.jsx"], "sourcesContent": ["export async function GET(req) {\r\n  const { searchParams } = new URL(req.url);\r\n  const place_id = searchParams.get(\"place_id\");\r\n\r\n  if (!place_id) {\r\n    return Response.json({ error: \"Missing place_id\" }, { status: 400 });\r\n  }\r\n\r\n  try {\r\n    const response = await fetch(\r\n      `https://rsapi.goong.io/Place/Detail?api_key=${process.env.GOONG_GEO_API_KEY}&place_id=${place_id}`\r\n    );\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to fetch place details\");\r\n    }\r\n\r\n    const data = await response.json();\r\n    return Response.json(data, { status: 200 });\r\n  } catch (error) {\r\n    return Response.json({ error: error.message }, { status: 500 });\r\n  }\r\n}"], "names": [], "mappings": ";;;AAAO,eAAe,IAAI,GAAG;IAC3B,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG;IACxC,MAAM,WAAW,aAAa,GAAG,CAAC;IAElC,IAAI,CAAC,UAAU;QACb,OAAO,SAAS,IAAI,CAAC;YAAE,OAAO;QAAmB,GAAG;YAAE,QAAQ;QAAI;IACpE;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,CAAC,4CAA4C,EAAE,QAAQ,GAAG,CAAC,iBAAiB,CAAC,UAAU,EAAE,UAAU;QAGrG,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,SAAS,IAAI,CAAC,MAAM;YAAE,QAAQ;QAAI;IAC3C,EAAE,OAAO,OAAO;QACd,OAAO,SAAS,IAAI,CAAC;YAAE,OAAO,MAAM,OAAO;QAAC,GAAG;YAAE,QAAQ;QAAI;IAC/D;AACF", "debugId": null}}]}