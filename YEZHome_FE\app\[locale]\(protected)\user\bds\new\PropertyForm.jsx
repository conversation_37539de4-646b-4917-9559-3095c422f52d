"use client";

import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useState, useEffect, useActionState, useRef, startTransition, useCallback } from "react";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { createProperty, updatePropertyById } from "@/app/actions/server/property";
import { propertyFormSchema } from "@/lib/schemas/propertyFormSchema";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CircleAlert, HelpCircle, ShieldAlert } from "lucide-react";
import { CAN_NOT_EDIT_STATUS, DEFAULT_POST_PRICE, FormType, highlightPrices, PropertyStatus, PropertyType } from "@/lib/enum";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Label } from "@/components/ui/label";

import { useTranslations } from "next-intl";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import CollapseHeader from "@/components/ui/collapse";
import AdditionalInformation from "@/components/user-property/AdditionalInformation";
import PropertyPostInformation from "@/components/user-property/PropertyPostInformation";
import PropertyMediaSection from "@/components/user-property/PropertyMediaSection";
import PropertyBasicInfoSection from "@/components/user-property/PropertyBasicInfoSection";
import AddressInput from "@/components/user-property/AddressInput";
import RankChangeDialog from "@/components/user-property/RankChangeDialog";
import CreatePropertyDetailInformation from "@/components/user-property/CreatePropertyDetailInformation";
import PricingDialog from "@/components/user-property/PricingDialog";
import LocationSelector from "@/components/user-property/LocationSelector";

import dynamic from "next/dynamic";
const SelectedLocationMap = dynamic(() => import("./components/SelectedLocationMap"), {
  ssr: false,
  loading: () => <div className="h-16 bg-white animate-pulse"></div>,
});

import PropertySaveButtons from "./components/PropertySaveButtons";

const initialState = {
  errors: null,
  message: null,
  fieldValues: {
    name: "",
    description: "",
  },
};

export default function PropertyForm({ property, formType = FormType.NEW }) {
  const t = useTranslations("PropertyForm");
  const router = useRouter();
  const { toast } = useToast();

  const [state, formAction, isPending] = useActionState(formType === FormType.NEW ? createProperty : updatePropertyById, initialState);

  const formRef = useRef(null);

  const [isFormDisabled, setIsFormDisabled] = useState(false);

  const form = useForm({
    resolver: zodResolver(propertyFormSchema),
    defaultValues: {
      formType: formType,
      propertyId: property?.id || "",
      videoUrl: property?.videoUrl || "",
      postType: property?.postType || PropertyType.SALE,
      propertyType: property?.propertyType || "",
      price: property?.price || "",
      cityId: property?.cityId?.toString() || "",
      districtId: property?.districtId?.toString() || "",
      wardId: property?.wardId?.toString() || "",
      address: property?.address || "",
      addressSelected: property?.addressSelected || "",
      name: property?.name || "",
      description: property?.description || "",
      area: property?.area || "",
      floors: property?.floors || "",
      rooms: property?.rooms || "",
      toilets: property?.toilets || "",
      direction: property?.direction || "",
      balconyDirection: property?.balconyDirection || "",
      legality: property?.legality || "",
      interior: property?.interior || "",
      width: property?.width || "",
      roadWidth: property?.roadWidth || "",
      latitude: property?.latitude || "",
      longitude: property?.longitude || "",
      placeData: property?.placeData || "",
      status: property?.status || PropertyStatus.DRAFT,
      isHighlighted: property?.isHighlighted || false,
      isAutoRenew: property?.isAutoRenew || false,
      ...(state?.fields ?? {}),
    },
    mode: "onChange",
    // disabled: isFormDisabled,
  });

  const [targetLocationNames, setTargetLocationNames] = useState(null);
  const selectedCity = form.watch("cityId");
  const selectedDistrict = form.watch("districtId");
  const selectedWard = form.watch("wardId");

  const locationSelectorRef = useRef(null);

  const [selectedLocation, setSelectedLocation] = useState(null);
  // Always initialize with an empty string to ensure it's controlled
  const [addressSelected, setAddressSelected] = useState("");

  const [uploadedFiles, setUploadedFiles] = useState([]);

  const [addressSelectedInputRef, setAddressSelectedInputRef] = useState(null);
  const [highlight, setHighlight] = useState(false);
  const [autoRenew, setAutoRenew] = useState(false);

  const [showRankChangeDialog, setShowRankChangeDialog] = useState(false);
  const [rankChangeDetails, setRankChangeDetails] = useState(null);
  const [basePostPrice, setBasePostPrice] = useState(DEFAULT_POST_PRICE);
  const rankRefreshRef = useRef(null);

  useEffect(() => {
    const isFormDisabled = formType === FormType.EDIT && CAN_NOT_EDIT_STATUS.includes(property?.status);
    setIsFormDisabled(isFormDisabled);
  }, []);

  useEffect(() => {
    if (property && formType === FormType.EDIT) {
      if (property.latitude && property.longitude) {
        setSelectedLocation({ latitude: parseFloat(property.latitude), longitude: parseFloat(property.longitude) });
      }

      // Ensure we always set a string value
      setAddressSelected(property.addressSelected || property.address || "");
      if (property.placeData) {
        try {
          const placeData = JSON.parse(property.placeData);
          if (placeData.result && placeData.result.formatted_address) {
            setAddressSelected(placeData.result.formatted_address || "");
          }
        } catch (error) {
          // Silent error handling
        }
      }
    }
  }, [property, formType]);

  useEffect(() => {
    if (state?.success === true) {
      toast({
        title: t("saveSuccess"),
        description: t("propertyUpdated"),
        className: "bg-teal-600 text-white",
      });

      const timeout = setTimeout(() => {
        if (formType === FormType.NEW) {
          if (state?.data?.status === PropertyStatus.DRAFT) {
            router.push("/user/bds");
          } else {
            router.push("/user/bds/new/success");
          }
        }
      }, 1000);

      return () => clearTimeout(timeout);
    } else if (state?.success === false) {
      toast({
        title: t("saveFailed"),
        description: state.message || t("propertyNotUpdated"),
        className: "bg-red-600 text-white",
      });
    }
  }, [state, router, formType, t]);

  useEffect(() => {
    if (property?.propertyMedia) {
      console.log("PropertyForm: Setting initial uploadedFiles from property.propertyMedia", property.propertyMedia);
      setUploadedFiles(property.propertyMedia);
    }
  }, [property]);

  // Debug: Log when uploadedFiles state changes
  useEffect(() => {
    console.log("PropertyForm: uploadedFiles state changed", uploadedFiles);
  }, [uploadedFiles]);

  const onUploadComplete = (files) => {
    console.log("PropertyForm: onUploadComplete parent", files);
    console.log("PropertyForm: Setting uploadedFiles state with", files.length, "files");
    setUploadedFiles(files);
  };

  useEffect(() => {
    const formValues = {
      formType: formType,
      propertyId: property?.id || "",
      videoUrl: property?.videoUrl || "",
      postType: property?.postType || PropertyType.SALE,
      propertyType: property?.propertyType || "",
      price: property?.price || "",
      cityId: property?.cityId?.toString() || "",
      districtId: property?.districtId?.toString() || "",
      wardId: property?.wardId?.toString() || "",
      address: property?.address || "",
      addressSelected: property?.addressSelected || "",
      name: property?.name || "",
      description: property?.description || "",
      area: property?.area || "",
      floors: property?.floors || "",
      rooms: property?.rooms || "",
      toilets: property?.toilets || "",
      direction: property?.direction || "",
      balconyDirection: property?.balconyDirection || "",
      legality: property?.legality || "",
      interior: property?.interior || "",
      width: property?.width || "",
      roadWidth: property?.roadWidth || "",
      latitude: property?.latitude || "",
      longitude: property?.longitude || "",
      placeData: property?.placeData || "",
      status: property?.status || PropertyStatus.DRAFT,
      isHighlighted: property?.isHighlighted || false,
      isAutoRenew: property?.isAutoRenew || false,
      ...(state?.fields ?? {}),
    };
    form.reset(formValues);
  }, [property, state?.fields, formType]);

  const onSubmit = useCallback(
    (values, action) => {
      const formData = new FormData(formRef.current);
      Object.keys(values).forEach((key) => formData.set(key, values[key]));

      console.log('PropertyForm: onSubmit uploadedFiles', uploadedFiles);
      console.log('PropertyForm: onSubmit uploadedFiles length', uploadedFiles.length);
      console.log('PropertyForm: onSubmit uploadedFiles JSON', JSON.stringify(uploadedFiles));

      formData.set("BasePostPrice", basePostPrice);
      formData.set("UploadedFiles", JSON.stringify(uploadedFiles));

      if (action === "saveDraft") {
        formData.set("status", PropertyStatus.DRAFT);
      } else {
        formData.set("status", PropertyStatus.PENDING_APPROVAL);
      }

      startTransition(() => formAction(formData));
    },
    [formRef, highlight, autoRenew, basePostPrice, uploadedFiles, formAction]
  );

  const handleSelectAddress = async (place_id, selectedPrediction, resolvedLocation) => {
    try {
      const responsePlaceData = await fetch(`/api/map/place-detail?place_id=${place_id}`);
      const data = await responsePlaceData.json();

      // Ensure we have a valid description string
      const description = selectedPrediction?.description || "";
      // Update state with the description
      setAddressSelected(description);

      const latitude = data?.result?.geometry?.location?.lat;
      const longitude = data?.result?.geometry?.location?.lng;

      setSelectedLocation({ latitude, longitude });

      // Update form values with safe values
      form.setValue("address", data?.result?.name || "");
      form.setValue("addressSelected", description);
      form.setValue("placeData", JSON.stringify(data?.result || {}));
      form.setValue("longitude", longitude || "");
      form.setValue("latitude", latitude || ""); // Handling resolved location from conflict dialog

      if (resolvedLocation && resolvedLocation.newCityId) {
        form.setValue("cityId", resolvedLocation.newCityId.toString());
        setTargetLocationNames({
          district: resolvedLocation.district,
          ward: resolvedLocation.ward,
        });
      }
    } catch (error) {
      toast({ variant: "destructive", description: t("fetchPlaceError") });
    }
  };

  const handleMarkerDragEnd = useCallback(
    async (markerPositionDragEnd) => {
      const { lat, lng } = markerPositionDragEnd;
      setSelectedLocation({ longitude: lng, latitude: lat });

      form.setValue("longitude", markerPositionDragEnd.lng);
      form.setValue("latitude", markerPositionDragEnd.lat);

      const controller = new AbortController();
      try {
        const response = await fetch(`/api/map/geocode?latlng=${lat},${lng}`, {
          signal: controller.signal,
        });
        if (!response.ok) throw new Error(t("handleMarkerDragEnd"));
        const data = await response.json();
        form.setValue("placeData", JSON.stringify(data?.results[0]));

      } catch (error) {
        if (error.name !== "AbortError") {
          toast({ variant: "destructive", description: error.message || t("handleMarkerDragEnd") });
        }
      }
      return () => controller.abort();
    },
    [form, toast, t]
  );

  const handleManualAddressClick = () => {
    if (addressSelectedInputRef) {
      addressSelectedInputRef.focus();
    }
  };

  const handleRankChange = useCallback(({ previousRank, currentRank }) => {
    const previousPrice = highlightPrices[previousRank] || DEFAULT_POST_PRICE;
    const currentPrice = highlightPrices[currentRank] || DEFAULT_POST_PRICE;

    if (previousPrice !== currentPrice) {
      setRankChangeDetails({
        previousRank,
        currentRank,
        previousPrice,
        currentPrice,
        isUpgrade: currentPrice < previousPrice,
      });
      setShowRankChangeDialog(true);
    }
  }, []);

  const handleConfirmPriceChange = () => {
    if (rankChangeDetails) {
      setBasePostPrice(rankChangeDetails.currentPrice);
    }
    setShowRankChangeDialog(false);
  };

  const handleCancelPriceChange = () => {
    setShowRankChangeDialog(false);
  };

  return (
    <div className={`inset-0 z-0 bg-slate-50 px-4 md:px-16 py-4`}>
      <Form {...form} className={`container ${isFormDisabled ? "cursor-not-allowed" : ""}`}>
        {state.message && (
          <Alert variant="destructive" className="mb-4">
            <CircleAlert className="h-4 w-4" />
            <AlertTitle>{t("createPostFailed")}</AlertTitle>
            <AlertDescription>{state.message}</AlertDescription>
          </Alert>
        )}
        {isFormDisabled && (
          <Alert className="mb-4 bg-yellow-50 border-yellow-200">
            <ShieldAlert className="h-6 w-6 text-yellow-800 mr-3" />
            <AlertTitle className="text-yellow-800">{t("postCannotBeEdited")}</AlertTitle>
            <AlertDescription className="text-yellow-700">{t("propertyCannotBeEdited")}</AlertDescription>
          </Alert>
        )}
        <form ref={formRef} onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <input type="hidden" name="propertyId" value={property?.id}></input>
            <input type="hidden" name="formType" value={formType}></input>
            <input type="hidden" name="status" value={property?.status}></input>
            {/* Property Information Section (Left) */}
            <div className="lg:col-span-2">
              <PropertyMediaSection
                form={form}
                property={property}
                uploadedFiles={uploadedFiles}
                setUploadedFiles={onUploadComplete}
                isFormDisabled={isFormDisabled}
              />

              <PropertyBasicInfoSection form={form} isFormDisabled={isFormDisabled} />

              <CollapseHeader title={t("addressSection")} subTitle={t("requiredInfo")}>
                <Separator className="mb-6" />
                <LocationSelector
                  ref={locationSelectorRef}
                  form={form}
                  isFormDisabled={isFormDisabled}
                  property={property}
                  formType={formType}
                  targetLocationNames={targetLocationNames}
                  setTargetLocationNames={setTargetLocationNames}
                />
                <div className="mt-3">
                  <div>
                    <AddressInput
                      form={form}
                      isFormDisabled={isFormDisabled}
                      selectedCity={selectedCity}
                      selectedDistrict={selectedDistrict}
                      selectedWard={selectedWard}
                      onAddressSelect={handleSelectAddress}
                      onManualAddressClick={handleManualAddressClick}
                      locationSelectorRef={locationSelectorRef}
                    />
                  </div>
                  <div className="mt-3">
                    <div>
                      <div className="flex gap-3 mb-3 items-center justify-between"></div>
                      <FormField
                        control={form.control}
                        name="addressSelected"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>
                              <div className="flex gap-3">
                                <Label htmlFor="address">{t("addressSelected")}</Label>
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <HelpCircle className="h-4 w-4 text-gray-500 cursor-help" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>{t("addressDescription")}</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            </FormLabel>
                            <FormControl>
                              <Input
                                ref={(el) => setAddressSelectedInputRef(el)}
                                placeholder={t("addressSelectedPlaceholder")}
                                {...field}
                                disabled={isFormDisabled}
                                readOnly={isFormDisabled}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </div>
              </CollapseHeader>

              <CollapseHeader title={t("map")} subTitle={t("mapDescription")}>
                <Separator className="mb-6" />
                <SelectedLocationMap
                  selectedLocation={selectedLocation}
                  isDisabled={isFormDisabled}
                  onMarkerDragEnd={!isFormDisabled ? handleMarkerDragEnd : undefined}
                />
              </CollapseHeader>

              <PropertyPostInformation form={form} isFormDisabled={isFormDisabled}></PropertyPostInformation>
              <AdditionalInformation form={form} isFormDisabled={isFormDisabled}></AdditionalInformation>
            </div>
            {/* Post Information Section (Right) */}
            <div>
              <Card className={`shadow-md sticky top-4`}>
                <CardHeader className="p-3">
                  <div className="flex justify-between items-center">
                    <h2 className="font-semibold">{t("postInformation")}</h2>
                    <PricingDialog />
                  </div>
                </CardHeader>
                <CardContent className="space-y-2">
                  <CreatePropertyDetailInformation
                    form={form}
                    property={property}
                    isFormDisabled={isFormDisabled}
                    basePostPrice={basePostPrice}
                    onRankChange={handleRankChange}
                    onRefreshRef={rankRefreshRef}
                  ></CreatePropertyDetailInformation>
                </CardContent>
                <CardFooter className="flex gap-4 justify-end">
                  <PropertySaveButtons
                    formType={formType}
                    propertyStatus={property?.status}
                    isPending={isPending}
                    formHandleSubmit={form.handleSubmit}
                    onSubmit={onSubmit}
                  />
                </CardFooter>
              </Card>
            </div>
          </div>
        </form>

        {/* Add the rank change dialog */}
        <RankChangeDialog
          open={showRankChangeDialog}
          onOpenChange={setShowRankChangeDialog}
          rankChangeDetails={rankChangeDetails}
          onConfirm={handleConfirmPriceChange}
          onCancel={handleCancelPriceChange}
        />
      </Form>
    </div>
  );
}
